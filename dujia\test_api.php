<?php
// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 测试配置
$base_url = 'http://localhost/api/dujia/';
$token = '143254321515132765'; // 使用文档中提到的token

// 测试数据
$test_data = [
    'name' => '张三',
    'idcard' => '110101199001011234',
    'wechat' => 'wxid_test123',
    'douyin' => 'douyin_test123',
    'kuaishou' => 'ks_test123'
];

// 测试函数
function test_api($url, $params, $description) {
    global $token;
    
    // 添加token到参数中
    $params['token'] = $token;
    
    // 构建完整URL
    $full_url = $url . '?' . http_build_query($params);
    
    echo "\n测试: $description\n";
    echo "URL: $full_url\n";
    
    // 发送请求
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $full_url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
    
    $response = curl_exec($ch);
    
    if (curl_errno($ch)) {
        echo "错误: " . curl_error($ch) . "\n";
        curl_close($ch);
        return;
    }
    
    curl_close($ch);
    
    if ($response === false) {
        echo "错误: 无法连接到服务器\n";
        return;
    }
    
    // 解析响应
    $result = json_decode($response, true);
    
    // 输出结果
    echo "响应:\n";
    echo json_encode($result, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
    echo "\n----------------------------------------\n";
}

// 测试所有接口
echo "开始API测试...\n";

// 1. 基础信息查询
test_api(
    $base_url . 'index.php',
    [
        'name' => $test_data['name'],
        'idcard' => $test_data['idcard'],
        'lx' => '1'
    ],
    '基础信息查询 - 民事诉讼信息'
);

test_api(
    $base_url . 'index.php',
    [
        'name' => $test_data['name'],
        'idcard' => $test_data['idcard'],
        'lx' => '2'
    ],
    '基础信息查询 - 户籍信息'
);

// 2. 社交媒体信息查询
test_api(
    $base_url . 'index.php',
    [
        'action' => 'generate_id',
        'wechat' => $test_data['wechat']
    ],
    '社交媒体信息查询 - 微信'
);

test_api(
    $base_url . 'index.php',
    [
        'action' => 'generate_id',
        'douyin' => $test_data['douyin']
    ],
    '社交媒体信息查询 - 抖音'
);

test_api(
    $base_url . 'index.php',
    [
        'action' => 'generate_id',
        'kuaishou' => $test_data['kuaishou']
    ],
    '社交媒体信息查询 - 快手'
);

// 3. 婚姻信息查询
test_api(
    $base_url . 'index.php',
    [
        'action' => 'generate_marriage',
        'name' => $test_data['name'],
        'idcard' => $test_data['idcard']
    ],
    '婚姻信息查询'
);

// 4. 家庭信息查询
test_api(
    $base_url . 'family.php',
    [
        'name' => $test_data['name'],
        'idcard' => $test_data['idcard']
    ],
    '家庭信息查询'
);

// 5. 地址信息查询
test_api(
    $base_url . 'family.php',
    [
        'name' => $test_data['name'],
        'idcard' => $test_data['idcard']
    ],
    '地址信息查询'
);

echo "\n测试完成！\n";
?> 