<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>增强版社工查询API演示</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .feature-box {
            background: #f8f9fa;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            border-left: 4px solid #007bff;
        }
        .demo-form {
            background: #e3f2fd;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="text"] {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 16px;
        }
        button {
            background: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background: #0056b3;
        }
        .result-box {
            background: #f8f9fa;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            border: 1px solid #dee2e6;
            white-space: pre-wrap;
            font-family: monospace;
            max-height: 500px;
            overflow-y: auto;
        }
        .example {
            background: #fff3cd;
            padding: 15px;
            margin: 10px 0;
            border-radius: 6px;
            border-left: 4px solid #ffc107;
        }
        .highlight {
            background: #d4edda;
            padding: 2px 6px;
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 增强版社工查询API演示</h1>
        
        <div class="feature-box">
            <h2>✨ 新增功能特点</h2>
            <ul>
                <li><strong>🔍 身份证智能扩展</strong>：自动提取出生日期、年龄、性别、星座、归属地区</li>
                <li><strong>📱 手机号智能扩展</strong>：自动识别运营商、号段信息</li>
                <li><strong>🔗 关联查询扩展</strong>：跨表关联查询，发现更多相关数据</li>
                <li><strong>📊 统计分析功能</strong>：提供详细的查询统计和数据分析</li>
            </ul>
        </div>

        <div class="demo-form">
            <h3>🧪 API测试工具</h3>
            <div class="form-group">
                <label for="query">查询内容：</label>
                <input type="text" id="query" placeholder="输入身份证号、手机号、姓名或QQ号">
            </div>
            <div class="form-group">
                <label for="token">Token：</label>
                <input type="text" id="token" placeholder="输入您的访问token">
            </div>
            <button onclick="performQuery()">🔍 执行查询</button>
            <button onclick="clearResult()">🗑️ 清空结果</button>
        </div>

        <div class="example">
            <h4>📝 测试示例</h4>
            <p><strong>身份证测试：</strong> <span class="highlight">110101199001011234</span></p>
            <p><strong>手机号测试：</strong> <span class="highlight">13812345678</span></p>
            <p><strong>姓名测试：</strong> <span class="highlight">张三</span></p>
            <p><strong>QQ号测试：</strong> <span class="highlight">123456789</span></p>
        </div>

        <div id="result" class="result-box" style="display: none;">
            <h4>📤 查询结果：</h4>
            <div id="resultContent"></div>
        </div>

        <div class="feature-box">
            <h3>🎯 API接口说明</h3>
            <p><strong>请求地址：</strong> <code>GET /extend/api/sgzh/index.php</code></p>
            <p><strong>请求参数：</strong></p>
            <ul>
                <li><code>msg</code> - 查询内容（必填）</li>
                <li><code>token</code> - 用户token（必填）</li>
            </ul>
            <p><strong>返回格式：</strong> JSON</p>
        </div>

        <div class="feature-box">
            <h3>📋 支持的查询类型</h3>
            <ul>
                <li><strong>身份证查询</strong>：18位身份证号码，自动扩展个人信息</li>
                <li><strong>手机号查询</strong>：11位手机号码，自动识别运营商信息</li>
                <li><strong>姓名查询</strong>：中文姓名，支持模糊匹配</li>
                <li><strong>QQ号查询</strong>：6-12位数字，查询相关账号信息</li>
            </ul>
        </div>
    </div>

    <script>
        function performQuery() {
            const query = document.getElementById('query').value.trim();
            const token = document.getElementById('token').value.trim();
            
            if (!query || !token) {
                alert('请填写查询内容和token');
                return;
            }
            
            const resultDiv = document.getElementById('result');
            const resultContent = document.getElementById('resultContent');
            
            resultDiv.style.display = 'block';
            resultContent.innerHTML = '🔄 查询中，请稍候...';
            
            // 构建API请求URL
            const apiUrl = `index.php?msg=${encodeURIComponent(query)}&token=${encodeURIComponent(token)}`;
            
            fetch(apiUrl)
                .then(response => response.json())
                .then(data => {
                    resultContent.innerHTML = JSON.stringify(data, null, 2);
                })
                .catch(error => {
                    resultContent.innerHTML = `❌ 查询失败: ${error.message}`;
                });
        }
        
        function clearResult() {
            document.getElementById('result').style.display = 'none';
            document.getElementById('resultContent').innerHTML = '';
        }
        
        // 示例数据填充
        function fillExample(type) {
            const examples = {
                'idcard': '110101199001011234',
                'phone': '13812345678',
                'name': '张三',
                'qq': '123456789'
            };
            document.getElementById('query').value = examples[type];
        }
    </script>
</body>
</html>
