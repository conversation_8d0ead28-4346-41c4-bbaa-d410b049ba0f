# 增强版社工查询API说明文档

## 🚀 功能概述

本增强版API在原有查询功能基础上，新增了智能数据扩展和关联查询功能，能够根据查询到的身份证和手机号自动扩展更多有价值的信息。

## ✨ 新增功能

### 1. 身份证智能扩展
当查询结果包含身份证号时，系统会自动提取以下信息：
- **出生日期**：从身份证号中提取准确的出生日期
- **年龄**：根据出生日期计算当前年龄
- **性别**：根据身份证规则判断性别（男/女）
- **星座**：根据出生日期计算对应星座
- **归属地区**：根据身份证前6位识别发证地区

### 2. 手机号智能扩展
当查询结果包含手机号时，系统会自动识别：
- **运营商**：识别手机号所属运营商（移动/联通/电信）
- **号段**：显示手机号前3位号段信息

### 3. 关联查询扩展
- **身份证关联查询**：通过身份证在所有数据表中查找关联的手机号
- **手机号关联查询**：通过手机号在所有数据表中查找关联的身份证
- **跨表数据融合**：整合来自不同数据源的相关信息

### 4. 统计分析功能
- **查询统计**：显示原始记录数、扩展信息数、关联信息数
- **数据计数**：统计找到的身份证和手机号数量
- **性能监控**：保持查询执行时间统计

## 📋 API接口

### 请求方式
```
GET /extend/api/sgzh/index.php
```

### 请求参数
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| msg | string | 是 | 查询内容（身份证/手机号/姓名/QQ号） |
| token | string | 是 | 用户认证token |

### 支持的查询格式
- **身份证**：18位身份证号码（如：110101199001011234）
- **手机号**：11位手机号码（如：13812345678）
- **姓名**：中文姓名（如：张三）
- **QQ号**：6-12位数字（如：123456789）

## 📤 返回格式

### 成功响应
```json
{
    "code": 200,
    "message": "查询成功（已智能扩展）",
    "shuju": "原始查询结果 + 智能扩展信息 + 关联查询结果",
    "execution_time": "0.1234 秒",
    "enhanced": true,
    "stats": {
        "original_records": 2,
        "enhanced_info": 3,
        "cross_reference": 1,
        "id_cards_found": 2,
        "phones_found": 3
    }
}
```

### 响应字段说明
- `code`：状态码（200成功，404无记录，400参数错误）
- `message`：响应消息
- `shuju`：查询结果数据（包含原始数据和扩展信息）
- `execution_time`：查询执行时间
- `enhanced`：是否启用了增强功能
- `stats`：统计信息对象

## 🎯 使用示例

### 示例1：身份证查询
```bash
curl "http://your-domain.com/extend/api/sgzh/index.php?msg=110101199001011234&token=your_token"
```

**返回结果包含：**
- 原始数据库记录
- 🔍智能扩展→身份证分析（出生日期、年龄、性别、星座、归属地区）
- 🔗关联扩展→通过身份证找到的手机号及其分析
- 📊查询统计

### 示例2：手机号查询
```bash
curl "http://your-domain.com/extend/api/sgzh/index.php?msg=13812345678&token=your_token"
```

**返回结果包含：**
- 原始数据库记录
- 🔍智能扩展→手机号分析（运营商、号段）
- 🔗关联扩展→通过手机号找到的身份证及其分析
- 📊查询统计

## 🔧 技术特点

1. **向后兼容**：完全兼容原有API接口，不影响现有调用
2. **性能优化**：智能扩展功能不影响原有查询性能
3. **数据去重**：自动去除重复的扩展信息
4. **错误处理**：完善的异常处理和错误提示
5. **编码支持**：完整支持中文字符和特殊符号

## 📊 数据源

系统支持以下数据表的查询和扩展：
- 48plc（包含出生日期、性别、地址等详细信息）
- chphone（电话号码数据）
- 随申码（上海随申码数据）
- 学习通（教育平台数据）
- 上海10E（上海地区数据）
- 浙江学籍（浙江学籍数据）
- aurora独家数据
- 银联数据
- 手机源
- 学籍数据
- B站数据
- 三要素数据

## 🛡️ 安全说明

- 保持原有的VIP验证和限流机制
- 支持Redis限流控制
- 完整的参数验证和SQL注入防护
- 敏感信息处理符合数据保护要求

## 🔧 依赖说明

### 专业数据包（可选）
系统支持两种运行模式：

#### 1. 专业模式（推荐）
安装以下Composer包获得最准确的数据：
```bash
composer require jxlwqq/id-validator
composer require shitoudev/phone-location
```

**优势：**
- 🎯 精确的身份证验证和信息提取
- 📍 详细的手机号归属地信息（省份、城市、邮编、区号）
- 🗓️ 完整的星座、生肖计算
- 🏛️ 权威的行政区划数据

#### 2. 简化模式（兼容）
如果无法安装专业包，系统会自动降级到内置简化版本：
- ✅ 基础的身份证信息提取
- ✅ 常用手机号段识别
- ✅ 简单的星座计算
- ✅ 基础的地区判断

### 智能切换机制
- 系统会自动检测专业包是否可用
- 优先使用专业包，出错时自动降级
- 在返回结果中标明使用的数据源

## 🔄 更新日志

### v2.1 修复版
- 🔧 修复vendor路径问题
- 🔧 添加专业包智能检测机制
- 🔧 增强错误处理和降级机制
- 🔧 扩展手机号段支持
- 🔧 添加数据来源标识

### v2.0 增强版
- ✅ 新增身份证智能扩展功能
- ✅ 新增手机号智能扩展功能
- ✅ 新增关联查询扩展功能
- ✅ 新增统计分析功能
- ✅ 优化数据展示格式
- ✅ 增加测试页面和文档

## 🧪 测试文件

- `test_simple.php` - 基础功能测试
- `demo.html` - 可视化演示页面
- `test_enhanced.php` - 完整功能测试（需要专业包）

---

**开发者**: 增强版API基于原有系统扩展开发，保持了原有的稳定性和性能，同时大幅提升了数据的价值和可用性。支持专业包和简化版本的智能切换，确保在任何环境下都能正常运行。
