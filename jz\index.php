<?php
require __DIR__ . '/vendor/autoload.php';

use <PERSON><PERSON><PERSON>\Phone\PhoneLocation;
use Jxlwqq\IdValidator\IdValidator;

require __DIR__ . '/address.php';
require __DIR__ . '/names.php';
use <PERSON><PERSON><PERSON>\NameGenerator;

// 允许跨域访问
header('Access-Control-Allow-Origin: *');
header('Content-Type: application/json; charset=utf-8');

// 获取token参数
$token = $_GET['token'] ?? '';

// 检查token是否为空
if (empty($token)) {
    echo json_encode([
        'code' => 400,
        'message' => '请提供有效的token。'
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

// 包含验证VIP逻辑
include './verify_vip.php';

// 自定义限制（可以自由组合）
$vipTimeLimit = true;  // 是否启用会员时间限制
$vipCodeLimit = true;  // 是否启用会员功能限制

// 定义回调函数
$callback = function($vipcode, $viptime) use ($vipTimeLimit, $vipCodeLimit) {
    if ($vipCodeLimit) {
        // 如果启用会员功能限制，执行相应的检查
        $result = vipCodeLimit($vipcode);
        if ($result !== true) return $result;  // 如果验证失败，返回错误信息
    }

    if ($vipTimeLimit) {
        // 如果启用会员时间限制，执行相应的检查
        $result = vipTimeLimit($viptime);
        if ($result !== true) return $result;  // 如果验证失败，返回错误信息
    }

    return true;  // 如果没有任何限制失败，返回true
};

// 调用验证函数
$verificationResult = verifyVipStatus($token, $callback);

// 如果返回的是错误信息，则输出
if ($verificationResult !== true) {
    echo json_encode($verificationResult, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
    exit;
}

// 获取手机号参数
$phone = $_GET['phone'] ?? '';

// 验证手机号格式
if (!preg_match('/^1[3-9]\d{9}$/', $phone)) {
    echo json_encode([
        'code' => 400,
        'message' => '手机号格式不正确',
        'data' => null
    ]);
    exit;
}

try {
    // 查询手机号归属地
    $phoneLocation = new PhoneLocation();
    $location = $phoneLocation->find($phone);
    
    // 初始化身份证验证器
    $idValidator = new IdValidator();
    
    // 随机生成性别（0: 女, 1: 男）
    $gender = rand(0, 1);
    
    // 生成符合归属地的身份证
    $fakeId = $idValidator->fakeId(true, $location['city']."市", $gender);
    
    // 生成随机姓名
    $name = NameGenerator::generate('rand');
    
    echo json_encode([
        'code' => 200,
        'message' => 'success',
        'data' => [
            'phone' => $phone,
            'location' => $location,
            'idcard' => $fakeId,
            'name' => $name,
            'address' => generateAddress($fakeId),
        '官方TG频道' => '官方TG频道@idatas8'
        ]
    ], JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    echo json_encode([
        'code' => 500,
        'message' => $e->getMessage(),
        'data' => null
    ]);
}