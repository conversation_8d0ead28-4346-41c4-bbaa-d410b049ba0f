INSERT INTO `api_list` (`id`, `name`, `api_url`, `des`, `http_mode`, `http_case`, `return_format`, `http_param`, `return_param`, `return_case`, `code_case`, `sign`, `type`, `state`, `pv`, `add_time`) VALUES (13, 'VIP-【地区猎魔】-火爆', 'dqlm', '传递地区,姓名,猎魔出这个地区所有为该人物的任何信息', 'GET', 'dqlm?token=xxxxxxx&name=姓名&diqu=地区', 'JSON', '<!-- 系统推荐以下表单均使用此表格样式 -->\n<thead>\n	<tr>\n		<th>名称</th>\n		<th>必填</th>\n		<th>类型</th>\n		<th>说明</th>\n	</tr>\n</thead>\n<tbody>\n	<tr>\n		<td>token</td>\n		<td>是</td>\n		<td>string</td>\n		<td>您的Token</td>\n	</tr>\n<tr>\n		<td>name</td>\n		<td>是</td>\n		<td>string</td>\n		<td>姓名</td>\n	</tr>\n<tr>\n		<td>diqu</td>\n		<td>是</td>\n		<td>string</td>\n		<td>地区(例如:深圳)</td>\n	</tr>\n</tbody>', '<!-- 系统推荐以下表单均使用此表格样式 -->\n<thead>\n    <tr>\n        <th>名称</th>\n        <th>类型</th>\n        <th>说明</th>\n    </tr>\n    </thead>\n    <tbody>\n    <tr>\n        <td>code</td>\n        <td>string</td>\n        <td>状态码</td>\n    </tr>\n <tr>\n        <td>message</td>\n        <td>string</td>\n        <td>状态信息</td>\n    </tr>\n <tr>\n        <td>shuju</td>\n        <td>string</td>\n        <td>地区猎魔结果</td>\n    </tr>\n <tr>\n        <td>execution_time</td>\n        <td>string</td>\n        <td>耗时</td>\n    </tr>\n\n    </tbody>', '{\n  \"code\": 200,\n  \"message\": \"查询成功，更多接口在频道@nfgzs\",\n  \"shuju\": \"猎魔结果\",\n  \"execution_time\": \"0.0355 秒\"\n}', 'Hello Word', 'dqlm', 'local', 'on', 12426, '2024-12-16 08:07:02');
INSERT INTO `api_list` (`id`, `name`, `api_url`, `des`, `http_mode`, `http_case`, `return_format`, `http_param`, `return_param`, `return_case`, `code_case`, `sign`, `type`, `state`, `pv`, `add_time`) VALUES (17, 'VIP-【实时定位】火热上新', 'dw', '传递手机号,输出定位信息', 'GET', 'dw?phone=要查询的手机号&token=你的Token', 'Json', '<thead>\n<tr>\n<th>名称</th>\n<th>必填</th>\n<th>类型</th>\n<th>说明</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>phone</td>\n<td>是</td>\n<td>string</td>\n<td>需要查询的手机号，如：13800138000</td>\n</tr>\n<tr>\n<td>token</td>\n<td>是</td>\n<td>string</td>\n<td>用户鉴权token</td>\n</tr>\n</tbody>', '<thead>\n<tr>\n<th>名称</th>\n<th>类型</th>\n<th>说明</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>code</td>\n<td>int</td>\n<td>状态码，200为成功，其他为失败</td>\n</tr>\n<tr>\n<td>message</td>\n<td>string</td>\n<td>提示信息</td>\n</tr>\n<tr>\n<td>data</td>\n<td>object</td>\n<td>返回数据对象</td>\n</tr>\n<tr>\n<td>data.province</td>\n<td>string</td>\n<td>归属地省份</td>\n</tr>\n<tr>\n<td>data.city</td>\n<td>string</td>\n<td>归属地城市</td>\n</tr>\n<tr>\n<td>data.operator</td>\n<td>string</td>\n<td>运营商</td>\n</tr>\n<tr>\n<td>data.lat</td>\n<td>float</td>\n<td>纬度（含GPS误差微调）</td>\n</tr>\n<tr>\n<td>data.lng</td>\n<td>float</td>\n<td>经度（含GPS误差微调）</td>\n</tr>\n<tr>\n<td>data.address</td>\n<td>string</td>\n<td>详细地址（含小区名、门牌号、方向距离、地标等）</td>\n</tr>\n<tr>\n<td>data.bing_map_url</td>\n<td>string</td>\n<td>必应地图访问链接（随机缩放级别12-20）</td>\n</tr>\n<tr>\n<td>data.signal_strength</td>\n<td>string</td>\n<td>信号强度，如\"-65dBm\"</td>\n</tr>\n<tr>\n<td>data.gps_accuracy</td>\n<td>string</td>\n<td>GPS精度，如\"12m\"</td>\n</tr>\n<tr>\n<td>data.battery_level</td>\n<td>string</td>\n<td>电池电量，如\"78%\"</td>\n</tr>\n<tr>\n<td>data.network_type</td>\n<td>string</td>\n<td>网络类型，如\"5G\"、\"4G\"、\"3G\"</td>\n</tr>\n<tr>\n<td>data.cell_tower_id</td>\n<td>string</td>\n<td>基站ID，如\"CELL_3456\"</td>\n</tr>\n<tr>\n<td>data.timestamp</td>\n<td>int</td>\n<td>时间戳</td>\n</tr>\n<tr>\n<td>data.speed</td>\n<td>string</td>\n<td>移动速度，如\"25km/h\"</td>\n</tr>\n<tr>\n<td>data.heading</td>\n<td>string</td>\n<td>方向角度，如\"180°\"</td>\n</tr>\n<tr>\n<td>data.altitude</td>\n<td>string</td>\n<td>海拔高度，如\"320m\"</td>\n</tr>\n<tr>\n<td>data.location_type</td>\n<td>string</td>\n<td>定位类型，固定为\"GPS+基站定位\"</td>\n</tr>\n<tr>\n<td>data.last_update</td>\n<td>string</td>\n<td>最后更新时间，格式\"Y-m-d H:i:s\"</td>\n</tr>\n</tbody>', '{\n  \"code\": 200,\n  \"message\": \"查询成功\",\n  \"data\": {\n    \"province\": \"广东\",\n    \"city\": \"深圳\",\n    \"operator\": \"中国移动\",\n    \"lat\": 22.543096,\n    \"lng\": 114.057865,\n    \"address\": \"广东省深圳市福田区深南大道华润大厦89号西北350米地铁站附近\",\n    \"bing_map_url\": \"https://www.bing.com/maps?cp=22.543096~114.057865&lvl=18\",\n    \"signal_strength\": \"-58dBm\",\n    \"gps_accuracy\": \"8m\",\n    \"battery_level\": \"85%\",\n    \"network_type\": \"5G\",\n    \"cell_tower_id\": \"CELL_7890\",\n    \"timestamp\": 1703123456,\n    \"speed\": \"15km/h\",\n    \"heading\": \"90°\",\n    \"altitude\": \"45m\",\n    \"location_type\": \"GPS+基站定位\",\n    \"last_update\": \"2023-12-21 15:30:45\"\n  }\n}', 'HelloWord', 'dw', 'local', 'on', 3509, '2025-07-12 23:09:30');
INSERT INTO `api_list` (`id`, `name`, `api_url`, `des`, `http_mode`, `http_case`, `return_format`, `http_param`, `return_param`, `return_case`, `code_case`, `sign`, `type`, `state`, `pv`, `add_time`) VALUES (1, 'VIP-【二要素核验】-权威企业口', 'eys', '传递姓名、身份证数据,核验身份证与姓名是否匹配.,对接了六大企业的二要素核验,24H人工轮班检测,出现问题马上修复,VIP用户没有任何限制,无IP限制！', 'GET', 'eys?token=XXXXX&name=姓名&idcard=身份证', 'JSON', '<!-- 系统推荐以下表单均使用此表格样式 -->\n<thead>\n	<tr>\n		<th>名称</th>\n		<th>必填</th>\n		<th>类型</th>\n		<th>说明</th>\n	</tr>\n</thead>\n<tbody>\n	<tr>\n		<td>token</td>\n		<td>是</td>\n		<td>string</td>\n		<td>您自己的Token</td>\n	</tr>\n<tr>\n		<td>name</td>\n		<td>是</td>\n		<td>string</td>\n		<td>预核验的姓名</td>\n	</tr>\n<tr>\n		<td>idcard</td>\n		<td>是</td>\n		<td>string</td>\n		<td>预核验的身份证号码</td>\n	</tr>\n</tbody>', '<!-- 系统推荐以下表单均使用此表格样式 -->\n<thead>\n    <tr>\n        <th>名称</th>\n        <th>类型</th>\n        <th>说明</th>\n    </tr>\n    </thead>\n    <tbody>\n    <tr>\n        <td>code</td>\n        <td>string</td>\n        <td>状态码</td>\n    </tr>\n   <tr>\n        <td>message</td>\n        <td>string</td>\n        <td>状态信息</td>\n    </tr>\n    </tbody>', '{\n    \"code\": 200,\n    \"Ps\": \"更多接口,及时更新-官方频道:@kmhsgk\",\n    \"message\": \"二要素核验正确\",\n    \"频道\": \"@kmhsgk\",\n    \"官网\": \"【及时更新,更多接口】https://qnm8.top/\"\n}\n', 'Hello Word', 'eys', 'local', 'on', 75156, '2024-12-02 01:33:40');
INSERT INTO `api_list` (`id`, `name`, `api_url`, `des`, `http_mode`, `http_case`, `return_format`, `http_param`, `return_param`, `return_case`, `code_case`, `sign`, `type`, `state`, `pv`, `add_time`) VALUES (7, 'VIP-【假地址个户】-模板①', 'gh1', '传递相关信息,自动匹配匹配年龄,性别,属性,地区,人脸照片.', 'GET', 'gh1?xm=姓名&hm=身份证号码&token=你的Token', 'JSON', '<!-- 系统推荐以下表单均使用此表格样式 -->\n<thead>\n	<tr>\n		<th>名称</th>\n		<th>必填</th>\n		<th>类型</th>\n		<th>说明</th>\n	</tr>\n</thead>\n<tbody>\n	\n<tr>\n		<td>xm</td>\n		<td>是</td>\n		<td>string</td>\n		<td>要处理的姓名</td>\n	</tr>\n<tr>\n		<td>hm</td>\n		<td>是</td>\n		<td>string</td>\n		<td>要处理的身份证号码</td>\n	</tr>\n<tr>\n		<th>token</th>\n		<th>是</th>\n		<th>text</th>\n		<th>你的Token</th>\n	</tr></tbody>', '<table>\n    <thead>\n        <tr>\n            <th>名称</th>\n            <th>类型</th>\n            <th>说明</th>\n        </tr>\n    </thead>\n    <tbody>\n        <tr>\n            <td>code</td>\n            <td>string</td>\n            <td>状态码，200表示处理成功</td>\n        </tr>\n        <tr>\n            <td>message</td>\n            <td>string</td>\n            <td>处理结果的描述信息</td>\n        </tr>\n        <tr>\n            <td>imgurl</td>\n            <td>string</td>\n            <td>生成的图像文件 URL</td>\n        </tr>\n        <tr>\n            <td>频道</td>\n            <td>string</td>\n            <td>官方频道信息</td>\n        </tr>\n        <tr>\n            <td>官网</td>\n            <td>string</td>\n            <td>提供接口和更新信息的官方网站</td>\n        </tr>\n    </tbody>\n</table>\n', '{\n  \"code\": \"200\",\n  \"message\": \"「处理完成,更多接口在频道@kmhsgk」\",\n  \"imgurl\": \"https://example.com/tmp/filename.jpg\",\n  \"频道\": \"@kmhsgk\",\n  \"官网\": \"【及时更新,更多接口】https://qnm8.top/\"\n}\n', 'Hello Word', 'gh1', 'local', 'on', 47231, '2024-12-02 02:03:16');
INSERT INTO `api_list` (`id`, `name`, `api_url`, `des`, `http_mode`, `http_case`, `return_format`, `http_param`, `return_param`, `return_case`, `code_case`, `sign`, `type`, `state`, `pv`, `add_time`) VALUES (8, 'VIP-【假地址个户】-模板②', 'gh2', '传递相关信息,自动匹配匹配年龄,性别,属性,地区,人脸照片.', 'GET', 'gh1?xm=姓名&mz=民族&hm=身份证&token=你的Token', 'JSON', '<!-- 系统推荐以下表单均使用此表格样式 -->\n<thead>\n	<tr>\n		<th>名称</th>\n		<th>必填</th>\n		<th>类型</th>\n		<th>说明</th>\n	</tr>\n</thead>\n<tbody>\n	\n<tr>\n		<td>xm</td>\n		<td>是</td>\n		<td>string</td>\n		<td>要处理的姓名</td>\n	</tr>\n<tr>\n		<td>mz</td>\n		<td>是</td>\n		<td>string</td>\n		<td>要处理的民族</td>\n	</tr>\n<tr>\n		<td>hm</td>\n		<td>是</td>\n		<td>string</td>\n		<td>要处理的身份证号码</td>\n	</tr>\n<tr>\n		<th>token</th>\n		<th>是</th>\n		<th>text</th>\n		<th>你的Token</th>\n	</tr>\n</tbody>', '<table>\n    <thead>\n        <tr>\n            <th>名称</th>\n            <th>类型</th>\n            <th>说明</th>\n        </tr>\n    </thead>\n    <tbody>\n        <tr>\n            <td>code</td>\n            <td>string</td>\n            <td>状态码，200表示处理成功</td>\n        </tr>\n        <tr>\n            <td>message</td>\n            <td>string</td>\n            <td>处理结果的描述信息</td>\n        </tr>\n        <tr>\n            <td>imgurl</td>\n            <td>string</td>\n            <td>生成的图像文件 URL</td>\n        </tr>\n        <tr>\n            <td>频道</td>\n            <td>string</td>\n            <td>官方频道信息</td>\n        </tr>\n        <tr>\n            <td>官网</td>\n            <td>string</td>\n            <td>提供接口和更新信息的官方网站</td>\n        </tr>\n    </tbody>\n</table>\n<table>\n    <thead>\n        <tr>\n            <th>名称</th>\n            <th>类型</th>\n            <th>说明</th>\n        </tr>\n    </thead>\n    <tbody>\n        <tr>\n            <td>code</td>\n            <td>string</td>\n            <td>状态码，200表示处理成功</td>\n        </tr>\n        <tr>\n            <td>message</td>\n            <td>string</td>\n            <td>处理结果的描述信息</td>\n        </tr>\n        <tr>\n            <td>imgurl</td>\n            <td>string</td>\n            <td>生成的图像文件 URL</td>\n        </tr>\n        <tr>\n            <td>频道</td>\n            <td>string</td>\n            <td>官方频道信息</td>\n        </tr>\n        <tr>\n            <td>官网</td>\n            <td>string</td>\n            <td>提供接口和更新信息的官方网站</td>\n        </tr>\n    </tbody>\n</table>\n', '{\n  \"code\": \"200\",\n  \"message\": \"「处理完成,更多接口在频道@kmhsgk」\",\n  \"imgurl\": \"https://example.com/tmp/filename.jpg\",\n  \"频道\": \"@kmhsgk\",\n  \"官网\": \"【及时更新,更多接口】https://qnm8.top/\"\n}\n', 'Hello Word', 'gh2', 'local', 'on', 203075, '2024-12-02 01:33:40');
INSERT INTO `api_list` (`id`, `name`, `api_url`, `des`, `http_mode`, `http_case`, `return_format`, `http_param`, `return_param`, `return_case`, `code_case`, `sign`, `type`, `state`, `pv`, `add_time`) VALUES (15, '智慧机主', 'jz', '传递手机号,处理出对于该手机号的姓名,身份证,地址', 'GET', '/jz?token=your_token&phone=13800138000', 'JSON', '<thead>\n<tr>\n<th>名称</th>\n<th>必填</th>\n<th>类型</th>\n<th>说明</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>token</td>\n<td>是</td>\n<td>string</td>\n<td>用户token</td>\n</tr>\n<tr>\n<td>phone</td>\n<td>是</td>\n<td>string</td>\n<td>手机号，如：13800138000</td>\n</tr>\n</tbody>', '<thead>\n<tr>\n<th>名称</th>\n<th>类型</th>\n<th>说明</th>\n</tr>\n</thead>\n<tbody>\n<tr>\n<td>code</td>\n<td>string</td>\n<td>状态码</td>\n</tr>\n<tr>\n<td>message</td>\n<td>string</td>\n<td>状态信息</td>\n</tr>\n<tr>\n<td>data</td>\n<td>object</td>\n<td>返回数据</td>\n</tr>\n</tbody>', '{\n    \"code\": \"200\",\n    \"message\": \"success\",\n    \"data\": {\n        \"phone\": \"13800138000\",\n        \"location\": {\n            \"province\": \"广东\",\n            \"city\": \"深圳\",\n            \"operator\": \"移动\"\n        },\n        \"idcard\": \"******************\",\n        \"name\": \"张三\",\n        \"address\": \"广东省深圳市南山区科技园\"\n    }\n}', 'Hello Word', 'jz', 'local', 'on', 1416, '2025-04-23 13:34:25');
INSERT INTO `api_list` (`id`, `name`, `api_url`, `des`, `http_mode`, `http_case`, `return_format`, `http_param`, `return_param`, `return_case`, `code_case`, `sign`, `type`, `state`, `pv`, `add_time`) VALUES (2, 'VIP-【身份证库补】-1202更新', 'kb', '通过正确的姓名以及模糊的身份证号根据数据库中的数据补齐出模糊位,模糊位用x代替,接口不限次数调用,平均每条耗时0.1431s补齐率85%', 'GET', 'kb?token=xxxxxxx&name=姓名&idcard=110101xxxxxxxxxx', 'JSON', '<!-- 系统推荐以下表单均使用此表格样式 -->\n<thead>\n	<tr>\n		<th>名称</th>\n		<th>必填</th>\n		<th>类型</th>\n		<th>说明</th>\n	</tr>\n</thead>\n<tbody>\n	<tr>\n		<td>token</td>\n		<td>是</td>\n		<td>string</td>\n		<td>您的Token</td>\n	</tr>\n<tr>\n		<td>name</td>\n		<td>是</td>\n		<td>string</td>\n		<td>要补齐信息的姓名</td>\n	</tr>\n<tr>\n		<td>idcard</td>\n		<td>是</td>\n		<td>string</td>\n		<td>模糊身份证号 x代表模糊位</td>\n	</tr>\n</tbody>', '<table>\n    <thead>\n        <tr>\n            <th>名称</th>\n            <th>类型</th>\n            <th>说明</th>\n        </tr>\n    </thead>\n    <tbody>\n        <tr>\n            <td>code</td>\n            <td>integer</td>\n            <td>状态码，200表示请求成功</td>\n        </tr>\n        <tr>\n            <td>message</td>\n            <td>string</td>\n            <td>状态信息，描述请求结果</td>\n        </tr>\n        <tr>\n            <td>msg</td>\n            <td>array</td>\n            <td>返回的消息数据，可能包含多条记录</td>\n        </tr>\n        <tr>\n            <td>频道</td>\n            <td>string</td>\n            <td>官方频道信息</td>\n        </tr>\n        <tr>\n            <td>官网</td>\n            <td>string</td>\n            <td>官网链接，包含更多接口信息</td>\n        </tr>\n    </tbody>\n</table>\n', '{\n  \"code\": 200,\n  \"message\": \"ok\",\n  \"msg\": [\n   //实际补齐结果\n  ],\n  \"频道\": \"@kmhsgk\",\n  \"官网\": \"【及时更新,更多接口】https://qnm8.top/\"\n}\n', 'Hello Word', 'kb', 'local', 'on', 6489, '2024-12-02 01:49:40');
INSERT INTO `api_list` (`id`, `name`, `api_url`, `des`, `http_mode`, `http_case`, `return_format`, `http_param`, `return_param`, `return_case`, `code_case`, `sign`, `type`, `state`, `pv`, `add_time`) VALUES (12, 'VIP-【空号检测】-实时检测', 'khjc', '传递手机号,返回手机号状态,实时状态：空号、实号、停机、库无、沉默号、风险号等', 'GET', 'khjc?token=xxxxx&phone=手机号', 'JSON', '<!-- 系统推荐以下表单均使用此表格样式 -->\n<thead>\n	<tr>\n		<th>名称</th>\n		<th>必填</th>\n		<th>类型</th>\n		<th>说明</th>\n	</tr>\n</thead>\n<tbody>\n	<tr>\n		<td>token</td>\n		<td>是</td>\n		<td>string</td>\n		<td>你的Token</td>\n	</tr>\n<tr>\n		<td>phone</td>\n		<td>是</td>\n		<td>string</td>\n		<td>检测的手机号</td>\n	</tr>\n</tbody>', '<!-- 系统推荐以下表单均使用此表格样式 -->\n<thead>\n    <tr>\n        <th>名称</th>\n        <th>类型</th>\n        <th>说明</th>\n    </tr>\n    </thead>\n    <tbody>\n    <tr>\n        <td>code</td>\n        <td>string</td>\n        <td>状态码</td>\n    </tr>\n <tr>\n        <td>message</td>\n        <td>string</td>\n        <td>状态信息</td>\n    </tr>\n <tr>\n        <td>shuju</td>\n        <td>string</td>\n        <td>检测结果</td>\n    </tr>\n    </tbody>', '{\n  \"code\": 200,\n  \"message\": \"查询成功\",\n  \"shuju\": \"手机号: 13812345678\n检测结果: 空号\n开通时间: 2017-06-15 14:23\n\",\n  \"频道\": \"@nfgzs\",\n  \"官网\": \"【及时更新,更多接口】https://qnm8.top/\"\n}\n', 'Hello Word', 'khjc', 'local', 'on', 535, '2024-12-14 03:07:48');
INSERT INTO `api_list` (`id`, `name`, `api_url`, `des`, `http_mode`, `http_case`, `return_format`, `http_param`, `return_param`, `return_case`, `code_case`, `sign`, `type`, `state`, `pv`, `add_time`) VALUES (4, 'VIP-【卡泡聆听】-身临其境', 'kp', '传递Token参数,获取一份卡泡录音', 'GET', 'kp?token=你的Token', 'JSON', '<!-- 系统推荐以下表单均使用此表格样式 -->\n<thead>\n	<tr>\n		<th>名称</th>\n		<th>必填</th>\n		<th>类型</th>\n		<th>说明</th>\n	</tr>\n</thead>\n<tbody>\n	<tr>\n		<th>token</th>\n		<th>是</th>\n		<th>text</th>\n		<th>你的Token</th>\n	</tr>\n</tbody>', '<table>\n    <thead>\n        <tr>\n            <th>名称</th>\n            <th>类型</th>\n            <th>说明</th>\n        </tr>\n    </thead>\n    <tbody>\n        <tr>\n            <td>code</td>\n            <td>string</td>\n            <td>状态码，\"200\"表示请求成功</td>\n        </tr>\n        <tr>\n            <td>message</td>\n            <td>string</td>\n            <td>状态信息，描述请求结果</td>\n        </tr>\n        <tr>\n            <td>mp3url</td>\n            <td>string</td>\n            <td>生成的MP3文件的URL路径</td>\n        </tr>\n        <tr>\n            <td>频道</td>\n            <td>string</td>\n            <td>官方频道信息</td>\n        </tr>\n        <tr>\n            <td>官网</td>\n            <td>string</td>\n            <td>官网链接，包含更多接口信息</td>\n        </tr>\n    </tbody>\n</table>\n', '{\n  \"code\": \"200\",\n  \"message\": \"「获取成功,更多接口在频道@kmhsgk」\",\n  \"mp3url\": \"https://example.com/luyin/filename.mp3\",\n  \"频道\": \"@kmhsgk\",\n  \"官网\": \"【及时更新,更多接口】https://qnm8.top/\"\n}\n', 'Hello Word', 'kp', 'local', 'on', 55898, '2024-12-02 01:52:18');
INSERT INTO `api_list` (`id`, `name`, `api_url`, `des`, `http_mode`, `http_case`, `return_format`, `http_param`, `return_param`, `return_case`, `code_case`, `sign`, `type`, `state`, `pv`, `add_time`) VALUES (3, 'VIP-【姓名猎魔】-14E库', 'lm', '传递姓名,通过姓名猎魔出该14E库中所有该名字的全部信息.,会员用户不限次数调用,无IP限制,24小时人工轮班检测,稳定率100%', 'GET', 'lm?token=xxxxxxx&msg=姓名', 'JSON', '<!-- 系统推荐以下表单均使用此表格样式 -->\n<thead>\n	<tr>\n		<th>名称</th>\n		<th>必填</th>\n		<th>类型</th>\n		<th>说明</th>\n	</tr>\n</thead>\n<tbody>\n	<tr>\n		<td>token</td>\n		<td>是</td>\n		<td>string</td>\n		<td>您的Token</td>\n	</tr>\n<tr>\n		<td>msg</td>\n		<td>是</td>\n		<td>string</td>\n		<td>欲猎魔姓名</td>\n	</tr>\n</tbody>', '<!-- 系统推荐以下表单均使用此表格样式 -->\n<thead>\n    <tr>\n        <th>名称</th>\n        <th>类型</th>\n        <th>说明</th>\n    </tr>\n    </thead>\n    <tbody>\n    <tr>\n        <td>code</td>\n        <td>string</td>\n        <td>状态码</td>\n    </tr>\n<tr>\n        <td>message</td>\n        <td>string</td>\n        <td>状态信息</td>\n    </tr>\n<tr>\n        <td>shuju</td>\n        <td>string</td>\n        <td>猎魔结果</td>\n    </tr>\n    </tbody>', '{\n  \"code\": 200,\n  \"message\": \"查询成功，更多接口在频道@kmhsgk\",\n  \"shuju\": \"消息1\n消息2\n消息3\", \n  \"execution_time\": \"0.123 秒\"\n}\n', 'Hello Word', 'lm', 'local', 'on', 93767, '2024-12-02 01:44:29');
INSERT INTO `api_list` (`id`, `name`, `api_url`, `des`, `http_mode`, `http_case`, `return_format`, `http_param`, `return_param`, `return_case`, `code_case`, `sign`, `type`, `state`, `pv`, `add_time`) VALUES (9, 'VIP-【QQ绑定查询】-16E', 'qq', '通过QQ查询出手机号,归属地,等相关信息.', 'GET', 'qq?qq=QQ号码&token=你的Token', 'JSON', '<!-- 系统推荐以下表单均使用此表格样式 -->\n<thead>\n	<tr>\n		<th>名称</th>\n		<th>必填</th>\n		<th>类型</th>\n		<th>说明</th>\n	</tr>\n</thead>\n<tbody>\n	<tr>\n		<td>qq</td>\n		<td>是</td>\n		<td>string</td>\n		<td>要查询的QQ号码</td>\n	</tr>\n<tr>\n		<th>token</th>\n		<th>是</th>\n		<th>text</th>\n		<th>你的Token</th>\n	</tr>\n</tbody>', '<!-- 系统推荐以下表单均使用此表格样式 -->\n<thead>\n    <tr>\n        <th>名称</th>\n        <th>类型</th>\n        <th>说明</th>\n    </tr>\n    </thead>\n    <tbody>\n    <tr>\n        <td>code</td>\n        <td>string</td>\n        <td>状态码</td>\n    </tr>\n    <tr>\n        <td>message</td>\n        <td>string</td>\n        <td>状态信息</td>\n    </tr>\n <tr>\n        <td>shuju</td>\n        <td>string</td>\n        <td>查询结果</td>\n    </tr>\n <tr>\n        <td>execution_time</td>\n        <td>string</td>\n        <td>查询耗时</td>\n    </tr>\n\n    </tbody>', '{\n  \"code\": 200,\n  \"message\": \"查询成功，更多接口在频道@kmhsgk\",\n  \"shuju\": \"QQ: 336699\n查询到以下结果↓:\n\nqq:336699\n手机号:18888888888\n归属地:福建省金门市移动\n\",\n  \"execution_time\": \"0.123 秒\"\n}\n', 'Hello Word', 'qq', 'local', 'on', 10980, '2024-12-02 14:48:14');
INSERT INTO `api_list` (`id`, `name`, `api_url`, `des`, `http_mode`, `http_case`, `return_format`, `http_param`, `return_param`, `return_case`, `code_case`, `sign`, `type`, `state`, `pv`, `add_time`) VALUES (5, 'VIP-【人脸核验】-无限制', 'rlhy', '传递姓名,身份证,图片外链.核验该信息是否与图片人脸符合,会员无限次数调用,无IP限制,稳定奔放中', 'GET', 'rlhy?token=xxxxx&name=姓名&idcard=身份证号码&imgurl=图片外链', 'JSON', '<!-- 系统推荐以下表单均使用此表格样式 -->\n<thead>\n	<tr>\n		<th>名称</th>\n		<th>必填</th>\n		<th>类型</th>\n		<th>说明</th>\n	</tr>\n</thead>\n<tbody>\n	<tr>\n		<td>token</td>\n		<td>是</td>\n		<td>string</td>\n		<td>您的 Token</td>\n	</tr>\n<tr>\n		<td>name</td>\n		<td>是</td>\n		<td>string</td>\n		<td>要核验的姓名</td>\n	</tr>\n<tr>\n		<td>idcard</td>\n		<td>是</td>\n		<td>string</td>\n		<td>要核验的身份证号码</td>\n	</tr>\n<tr>\n		<td>imgurl</td>\n		<td>是</td>\n		<td>string</td>\n		<td>要核验的图片外链</td>\n	</tr>\n</tbody>', '<table>\n    <thead>\n        <tr>\n            <th>名称</th>\n            <th>类型</th>\n            <th>说明</th>\n        </tr>\n    </thead>\n    <tbody>\n        <tr>\n            <td>code</td>\n            <td>int</td>\n            <td>状态码，\"200\"表示请求成功</td>\n        </tr>\n        <tr>\n            <td>message</td>\n            <td>string</td>\n            <td>状态信息，描述请求结果</td>\n        </tr>\n        <tr>\n            <td>name</td>\n            <td>string</td>\n            <td>用户姓名</td>\n        </tr>\n        <tr>\n            <td>idcard</td>\n            <td>string</td>\n            <td>身份证号码</td>\n        </tr>\n        <tr>\n            <td>similarity</td>\n            <td>float</td>\n            <td>姓名和身份证号的相似度</td>\n        </tr>\n        <tr>\n            <td>result</td>\n            <td>string</td>\n            <td>核验结果，例如“核验通过”或“核验失败”</td>\n        </tr>\n        <tr>\n            <td>execution_time</td>\n            <td>string</td>\n            <td>接口执行时间</td>\n        </tr>\n        <tr>\n            <td>频道</td>\n            <td>string</td>\n            <td>官方频道信息</td>\n        </tr>\n        <tr>\n            <td>官网</td>\n            <td>string</td>\n            <td>官网链接，包含更多接口信息</td>\n        </tr>\n    </tbody>\n</table>\n', '{\n  \"code\": 200,\n  \"message\": \"核验完成\",\n  \"name\": \"张三\",\n  \"idcard\": \"******************\",\n  \"similarity\": 98.5,\n  \"result\": \"核验通过\",\n  \"execution_time\": \"0.023s\",\n  \"频道\": \"@kmhsgk\",\n  \"官网\": \"【及时更新,更多接口】https://qnm8.top/\"\n}\n', 'Hello Word', 'rlhy', 'local', 'on', 1539, '2024-12-02 01:55:59');
INSERT INTO `api_list` (`id`, `name`, `api_url`, `des`, `http_mode`, `http_case`, `return_format`, `http_param`, `return_param`, `return_case`, `code_case`, `sign`, `type`, `state`, `pv`, `add_time`) VALUES (6, 'VIP-【社工综合】-百万社工库对接', 'sgzh', '传递手机号,身份证,查询出相关信息,包括但不限于:QQ绑定数据,QQ邮箱数据,LOL数据,密码数据,小红书数据,内涵段子,皮皮虾,学籍数据,车牌数据,婚姻数据,户籍数据,等等,会员用户无限次数调用,无IP限制,稳定奔放', 'GET', 'sgzh?token=xxxxxx&msg=姓名或手机号', 'JSON', '<!-- 系统推荐以下表单均使用此表格样式 -->\n<thead>\n	<tr>\n		<th>名称</th>\n		<th>必填</th>\n		<th>类型</th>\n		<th>说明</th>\n	</tr>\n</thead>\n<tbody>\n	<tr>\n		<td>token</td>\n		<td>是</td>\n		<td>string</td>\n		<td>您的 Token</td>\n	</tr>\n<tr>\n		<td>msg</td>\n		<td>是</td>\n		<td>string</td>\n		<td>要查询的姓名或手机号</td>\n	</tr>\n</tbody>', '<table>\n    <thead>\n        <tr>\n            <th>名称</th>\n            <th>类型</th>\n            <th>说明</th>\n        </tr>\n    </thead>\n    <tbody>\n        <tr>\n            <td>code</td>\n            <td>int</td>\n            <td>状态码，200表示查询成功</td>\n        </tr>\n        <tr>\n            <td>message</td>\n            <td>string</td>\n            <td>查询结果的描述信息</td>\n        </tr>\n        <tr>\n            <td>shuju</td>\n            <td>string</td>\n            <td>查询到的数据，多个数据之间使用换行符分隔</td>\n        </tr>\n        <tr>\n            <td>execution_time</td>\n            <td>string</td>\n            <td>查询执行的时间，单位为秒</td>\n        </tr>\n    </tbody>\n</table>\n', '{\n  \"code\": 200,\n  \"message\": \"查询成功，更多接口在频道@kmhsgk\",\n  \"shuju\": \"数据1\n数据2\n数据3\",\n  \"execution_time\": \"0.123 秒\"\n}\n', 'Hello Word', 'sgzh', 'local', 'on', 43616, '2024-12-02 01:59:39');
INSERT INTO `api_list` (`id`, `name`, `api_url`, `des`, `http_mode`, `http_case`, `return_format`, `http_param`, `return_param`, `return_case`, `code_case`, `sign`, `type`, `state`, `pv`, `add_time`) VALUES (10, 'VIP-【网红猎魔】-最新库', 'wh', '传递网红的微博,姓名,手机号,网名,查询出相关信息.', 'GET', 'wh?msg=查询内容&token=你的Token', 'JSON', '<!-- 系统推荐以下表单均使用此表格样式 -->\n<thead>\n	<tr>\n		<th>名称</th>\n		<th>必填</th>\n		<th>类型</th>\n		<th>说明</th>\n	</tr>\n</thead>\n<tbody>\n	\n<tr>\n		<td>msg</td>\n		<td>是</td>\n		<td>string</td>\n		<td>查询内容</td>\n	</tr>\n<tr>\n		<th>token</th>\n		<th>是</th>\n		<th>text</th>\n		<th>你的Token</th>\n	</tr>\n</tbody>', '<!-- 系统推荐以下表单均使用此表格样式 -->\n<thead>\n    <tr>\n        <th>名称</th>\n        <th>类型</th>\n        <th>说明</th>\n    </tr>\n    </thead>\n    <tbody>\n    <tr>\n        <td>code</td>\n        <td>string</td>\n        <td>状态码</td>\n    </tr>\n<tr>\n        <td>message</td>\n        <td>string</td>\n        <td>状态信息</td>\n    </tr>\n<tr>\n        <td>shuju</td>\n        <td>string</td>\n        <td>查询结果</td>\n    </tr>\n<tr>\n        <td>execution_time</td>\n        <td>string</td>\n        <td>查询耗时</td>\n    </tr>\n    </tbody>', '{\n  \"code\": 200,\n  \"message\": \"查询成功，更多接口在频道@kmhsgk\",\n  \"shuju\": \"宇将军 名字：王志宇 身份证：211282200005286039\",\n  \"execution_time\": \"0.0003 秒\",\n  \"频道\": \"@kmhsgk\",\n  \"官网\": \"【及时更新,更多接口】https://qnm8.top/\"\n}', 'Hello Word', 'wh', 'local', 'on', 8126, '2024-12-02 22:44:48');
INSERT INTO `api_list` (`id`, `name`, `api_url`, `des`, `http_mode`, `http_case`, `return_format`, `http_param`, `return_param`, `return_case`, `code_case`, `sign`, `type`, `state`, `pv`, `add_time`) VALUES (11, 'VIP-【刑事侦查】-高仿真', 'xszc', '传递姓名,身份证号码等信息,返回刑事侦查模板', 'GET', 'xszc?token=xxxxxxxx&xm=姓名&hm=身份证号码&ajxz=案件性质&ajlb=案件类别&danwei=工作单位&whcd=文化程度', 'JSON', '<!-- 系统推荐以下表单均使用此表格样式 -->\n<thead>\n	<tr>\n		<th>名称</th>\n		<th>必填</th>\n		<th>类型</th>\n		<th>说明</th>\n	</tr>\n</thead>\n<tbody>\n	<tr>\n		<td>token</td>\n		<td>是</td>\n		<td>string</td>\n		<td>你的token</td>\n	</tr>\n<tr>\n		<td>xm</td>\n		<td>是</td>\n		<td>string</td>\n		<td>姓名</td>\n	</tr>\n<tr>\n		<td>hm</td>\n		<td>是</td>\n		<td>string</td>\n		<td>身份证号码</td>\n	</tr>\n<tr>\n		<td>ajxz</td>\n		<td>是</td>\n		<td>string</td>\n		<td>案件性质</td>\n	</tr>\n<tr>\n		<td>ajlb</td>\n		<td>是</td>\n		<td>string</td>\n		<td>案件类别</td>\n	</tr>\n<tr>\n		<td>danwei</td>\n		<td>是</td>\n		<td>string</td>\n		<td>工作单位/td>\n	</tr>\n<tr>\n		<td>whcd</td>\n		<td>是</td>\n		<td>string</td>\n		<td>文化程度/td>\n	</tr>\n</tbody>\n<thead>\n	<tr>\n		<th>参数名</th>\n		<th>参数编码</th>\n		<th>说明</th>\n	</tr>\n</thead>\n</thead>\n<tbody>\n	<tr>\n		<td>ajlb</td>\n		<td>1、2</td>\n		<td>盗窃案、暴力案</td>\n	</tr>\n<tr>\n		<td>ajxz</td>\n		<td>1、2、3、4</td>\n		<td>轻微犯罪、普通犯罪、重大犯罪、特别重大犯罪</td>\n	</tr>\n</tbody>', '<!-- 系统推荐以下表单均使用此表格样式 -->\n<thead>\n    <tr>\n        <th>名称</th>\n        <th>类型</th>\n        <th>说明</th>\n    </tr>\n    </thead>\n    <tbody>\n    <tr>\n        <td>code</td>\n        <td>string</td>\n        <td>状态码</td>\n    </tr>\n <tr>\n        <td>message</td>\n        <td>string</td>\n        <td>状态信息</td>\n    </tr>\n <tr>\n        <td>imgurl</td>\n        <td>string</td>\n        <td>图片地址</td>\n    </tr>\n    </tbody>', '{\n\"code\":\"200\",\n\"message\":\"「处理完成,更多接口在频道@kmhsgk」\",\n\"imgurl\":\"https://xxxxxxxx.png\",\n\"频道\":\"@kmhsgk\",\n\"官网\":\"【及时更新,更多接口】https://qnm8.top/\"\n}', 'Hello Word', 'xszc', 'local', 'on', 464, '2024-12-04 17:48:04');
INSERT INTO `api_list` (`id`, `name`, `api_url`, `des`, `http_mode`, `http_case`, `return_format`, `http_param`, `return_param`, `return_case`, `code_case`, `sign`, `type`, `state`, `pv`, `add_time`) VALUES (16, '【火爆上新】身份证正反面', 'zfm', '传递相关信息,自动匹配匹配年龄,性别,属性,地区,人脸照片.签发机关,有效期', 'GET', 'zfm?xm=姓名&hm=身份证号码&token=你的Token', 'JSON', '<!-- 系统推荐以下表单均使用此表格样式 -->\n<thead>\n	<tr>\n		<th>名称</th>\n		<th>必填</th>\n		<th>类型</th>\n		<th>说明</th>\n	</tr>\n</thead>\n<tbody>\n	\n<tr>\n		<td>xm</td>\n		<td>是</td>\n		<td>string</td>\n		<td>要处理的姓名</td>\n	</tr>\n<tr>\n		<td>hm</td>\n		<td>是</td>\n		<td>string</td>\n		<td>要处理的身份证号码</td>\n	</tr>\n<tr>\n		<th>token</th>\n		<th>是</th>\n		<th>text</th>\n		<th>你的Token</th>\n	</tr></tbody>', '<table>\n    <thead>\n        <tr>\n            <th>名称</th>\n            <th>类型</th>\n            <th>说明</th>\n        </tr>\n    </thead>\n    <tbody>\n        <tr>\n            <td>code</td>\n            <td>string</td>\n            <td>状态码，200表示处理成功</td>\n        </tr>\n        <tr>\n            <td>message</td>\n            <td>string</td>\n            <td>处理结果的描述信息</td>\n        </tr>\n        <tr>\n            <td>imgurl</td>\n            <td>string</td>\n            <td>生成的图像文件 URL</td>\n        </tr>\n<tr>\n            <td>black_imgurl</td>\n            <td>string</td>\n            <td>正面</td>\n        </tr>\n        <tr>\n            <td>频道</td>\n            <td>string</td>\n            <td>官方频道信息</td>\n        </tr>\n        <tr>\n            <td>官网</td>\n            <td>string</td>\n            <td>提供接口和更新信息的官方网站</td>\n        </tr>\n    </tbody>\n</table>\n', '{\n  \"code\": \"200\",\n  \"message\": \"「处理完成」\",\n  \"imgurl\": \"http://127.0.0.10/tmp/身份证反面-陈亚军_130402197407222427.png\",\n  \"back_imgurl\": \"http://127.0.0.10/tmp/身份证正面-陈亚军_130402197407222427.png\",\n  \"官方TG频道\": \"官方TG频道@idatas8\"\n}', '注,\n民族 地址, 人脸照片 签发机关 有效期 都可以自定义\n民族(mz) \n地址(dz)\n大头(tp png或jpg链接) \n签发机关(jg) \n有效期(yxq 例如19900101-20000101 八个数字)', 'zfm', 'local', 'on', 9255, '2025-06-14 21:21:18');
INSERT INTO `api_list` (`id`, `name`, `api_url`, `des`, `http_mode`, `http_case`, `return_format`, `http_param`, `return_param`, `return_case`, `code_case`, `sign`, `type`, `state`, `pv`, `add_time`) VALUES (18, 'VIP-【临时空间】-创建功能', 'zyj', '会员用户创建临时空间，生成访问链接和查看密码。当有人访问链接时自动调用前置摄像头拍照并存储到本地', 'GET', 'zyj?token=你的Token&action=create', 'JSON', '<!-- 系统推荐以下表单均使用此表格样式 -->\n<thead>\n	<tr>\n		<th>名称</th>\n		<th>必填</th>\n		<th>类型</th>\n		<th>说明</th>\n	</tr>\n</thead>\n<tbody>\n	<tr>\n		<td>token</td>\n		<td>是</td>\n		<td>string</td>\n		<td>会员Token</td>\n	</tr>\n	<tr>\n		<td>action</td>\n		<td>是</td>\n		<td>string</td>\n		<td>操作类型，固定值"create"</td>\n	</tr>\n</tbody>', '<!-- 系统推荐以下表单均使用此表格样式 -->\n<thead>\n    <tr>\n        <th>名称</th>\n        <th>类型</th>\n        <th>说明</th>\n    </tr>\n</thead>\n<tbody>\n    <tr>\n        <td>code</td>\n        <td>int</td>\n        <td>状态码，200表示成功</td>\n    </tr>\n    <tr>\n        <td>message</td>\n        <td>string</td>\n        <td>状态信息</td>\n    </tr>\n    <tr>\n        <td>tpurl</td>\n        <td>string</td>\n        <td>临时空间访问链接</td>\n    </tr>\n    <tr>\n        <td>pw</td>\n        <td>string</td>\n        <td>查看密码</td>\n    </tr>\n</tbody>', '{\n  \"code\": 200,\n  \"message\": \"临时空间创建成功\",\n  \"tpurl\": \"http://yourdomain.com/tmpview/abc123def456\",\n  \"pw\": \"a1b2c3d4\"\n}', 'Hello Word', 'zyj', 'local', 'on', 0, '2025-07-30 12:00:00');
INSERT INTO `api_list` (`id`, `name`, `api_url`, `des`, `http_mode`, `http_case`, `return_format`, `http_param`, `return_param`, `return_case`, `code_case`, `sign`, `type`, `state`, `pv`, `add_time`) VALUES (19, 'VIP-【临时空间】-查看功能', 'zyj', '通过Token和密码查看临时空间中拍摄的所有照片，返回图片URL数组和拍摄次数', 'GET', 'zyj?token=你的Token&pw=查看密码&action=view', 'JSON', '<!-- 系统推荐以下表单均使用此表格样式 -->\n<thead>\n	<tr>\n		<th>名称</th>\n		<th>必填</th>\n		<th>类型</th>\n		<th>说明</th>\n	</tr>\n</thead>\n<tbody>\n	<tr>\n		<td>token</td>\n		<td>是</td>\n		<td>string</td>\n		<td>会员Token</td>\n	</tr>\n	<tr>\n		<td>pw</td>\n		<td>是</td>\n		<td>string</td>\n		<td>查看密码</td>\n	</tr>\n	<tr>\n		<td>action</td>\n		<td>是</td>\n		<td>string</td>\n		<td>操作类型，固定值"view"</td>\n	</tr>\n</tbody>', '<!-- 系统推荐以下表单均使用此表格样式 -->\n<thead>\n    <tr>\n        <th>名称</th>\n        <th>类型</th>\n        <th>说明</th>\n    </tr>\n</thead>\n<tbody>\n    <tr>\n        <td>code</td>\n        <td>int</td>\n        <td>状态码，200表示成功</td>\n    </tr>\n    <tr>\n        <td>message</td>\n        <td>string</td>\n        <td>状态信息</td>\n    </tr>\n    <tr>\n        <td>imgurl</td>\n        <td>array</td>\n        <td>图片URL数组</td>\n    </tr>\n    <tr>\n        <td>count</td>\n        <td>int</td>\n        <td>图片数量/拍摄次数</td>\n    </tr>\n</tbody>', '{\n  \"code\": 200,\n  \"message\": \"获取成功\",\n  \"imgurl\": [\n    \"http://yourdomain.com/extend/api/zyj/tpimg/abc123_1234567890_def456.jpg\",\n    \"http://yourdomain.com/extend/api/zyj/tpimg/abc123_1234567891_ghi789.jpg\"\n  ],\n  \"count\": 2\n}', 'Hello Word', 'zyj', 'local', 'on', 0, '2025-07-30 12:00:00');
