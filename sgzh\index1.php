<?php

include 'db.php';
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');
header('Content-Type: application/json; charset=utf-8');

// 获取动态参数
$msg = isset($_GET['msg']) ? $_GET['msg'] : '';
$token = isset($_GET['token']) ? $_GET['token'] : '';

// 检查参数
if (empty($msg) || empty($token)) {
    echo json_encode([
        'code' => 400,
        'message' => '请提供有效的参数和 token。'
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

// 计时器开始
$startTime = microtime(true);

// 确定查询内容的格式（不允许中文）
if (preg_match('/[\x{4e00}-\x{9fa5}]/u', $msg)) {
    echo json_encode([
        'code' => 400,
        'message' => '查询内容不能包含中文。'
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

// 包含验证 VIP 逻辑
include './verify_vip.php';

// 获取请求中的token
$token = $_GET['token'];  // 或者通过其他方式获取

// 自定义限制（可以自由组合）
$vipTimeLimit = false;  // 是否启用会员时间限制
$vipCodeLimit = true;  // 是否启用会员功能限制

// 定义回调函数
$callback = function($vipcode, $viptime) use ($vipTimeLimit, $vipCodeLimit) {
    if ($vipCodeLimit) {
        // 如果启用会员功能限制，执行相应的检查
        $result = vipCodeLimit($vipcode);
        if ($result !== true) return $result;  // 如果验证失败，返回错误信息
    }

    if ($vipTimeLimit) {
        // 如果启用会员时间限制，执行相应的检查
        $result = vipTimeLimit($viptime);
        if ($result !== true) return $result;  // 如果验证失败，返回错误信息
    }

    return true;  // 如果没有任何限制失败，返回true
};

// 调用验证函数
$verificationResult = verifyVipStatus($token, $callback);

// 如果返回的是错误信息，则输出
if ($verificationResult !== true) {
    echo json_encode($verificationResult, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
    exit;
}

$apiUrl = 'http://127.0.0.1:7987/?text=' . urlencode($msg);

$response = file_get_contents($apiUrl);
if ($response === false) {
    echo json_encode([
        'code' => 500,
        'message' => '出错联系客服'
    ], JSON_UNESCAPED_UNICODE);
    exit;
}


$responseData = json_decode($response, true);


if (isset($responseData['message']) && $responseData['message'] === "未查询到结果") {
    echo json_encode([
        'code' => 404,
        'message' => '库中无记录。',
        'execution_time' => round(microtime(true) - $startTime, 4) . ' 秒'
    ], JSON_UNESCAPED_UNICODE);
} elseif (isset($responseData['status']) && $responseData['status'] === 'success') {
    // 如果查询成功并且返回数据
    if (isset($responseData['shuju']) && !empty($responseData['shuju'])) {
        // 对返回的 `shuju` 进行换行符处理
        $shuju = str_replace("\\n", "\n", $responseData['shuju']);
        echo json_encode([
            'code' => 200,
            'message' => '查询成功，更多接口在频道@nfgzs',
            'shuju' => $shuju,  // 返回查询结果，已经处理换行符
            'execution_time' => round(microtime(true) - $startTime, 4) . ' 秒'
        ], JSON_UNESCAPED_UNICODE);
    } else {
        echo json_encode([
            'code' => 404,
            'message' => '没有查询到有效的回复。',
            'execution_time' => round(microtime(true) - $startTime, 4) . ' 秒'
        ], JSON_UNESCAPED_UNICODE);
    }
} else {
    echo json_encode([
        'code' => 500,
        'message' => '查询失败，未返回有效数据。',
        'execution_time' => round(microtime(true) - $startTime, 4) . ' 秒'
    ], JSON_UNESCAPED_UNICODE);
}

?>
