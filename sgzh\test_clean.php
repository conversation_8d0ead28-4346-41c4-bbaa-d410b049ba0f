<?php
// 测试清理后的简洁版本
header('Content-Type: text/html; charset=utf-8');

echo "<h1>🧹 清理后的简洁版API测试</h1>";

include_once 'index.php';

echo "<h2>身份证信息提取测试</h2>";

$testIdCard = "110101199001011234";
$idCardInfo = extractIdCardInfo($testIdCard);

echo "<h3>测试身份证: $testIdCard</h3>";
echo "<div style='background:#f0f8ff; padding:15px; margin:10px 0; border-radius:8px;'>";
if (!empty($idCardInfo)) {
    foreach ($idCardInfo as $key => $value) {
        echo "<p><strong>$key:</strong> $value</p>";
    }
} else {
    echo "<p>无法提取信息</p>";
}
echo "</div>";

echo "<h2>手机号信息提取测试</h2>";

$testPhone = "13812345678";
$phoneInfo = getPhoneLocation($testPhone);

echo "<h3>测试手机号: $testPhone</h3>";
echo "<div style='background:#f0fff0; padding:15px; margin:10px 0; border-radius:8px;'>";
if (!empty($phoneInfo)) {
    foreach ($phoneInfo as $key => $value) {
        echo "<p><strong>$key:</strong> $value</p>";
    }
} else {
    echo "<p>无法提取信息</p>";
}
echo "</div>";

echo "<h2>✅ 清理完成</h2>";
echo "<ul>";
echo "<li>❌ 移除了 stats 参数</li>";
echo "<li>❌ 移除了系统信息</li>";
echo "<li>❌ 移除了数据来源标识</li>";
echo "<li>❌ 移除了说明文字</li>";
echo "<li>✅ 保留了核心功能和扩展信息</li>";
echo "</ul>";

echo "<h2>API返回格式</h2>";
echo "<p>现在API返回的是简洁的格式：</p>";
echo "<pre>";
echo json_encode([
    'code' => 200,
    'message' => '查询成功',
    'shuju' => '原始数据 + 智能扩展信息 + 关联查询结果',
    'execution_time' => '0.1234 秒'
], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
echo "</pre>";

?>

<style>
body {
    font-family: 'Microsoft YaHei', Arial, sans-serif;
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    background-color: #f9f9f9;
}
h1, h2, h3 {
    color: #333;
}
pre {
    background: #f4f4f4;
    padding: 15px;
    border-radius: 8px;
    overflow-x: auto;
}
ul {
    background: #fff;
    padding: 15px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}
</style>
