<?php
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');
header('Content-Type: application/json; charset=utf-8');

// 引入配置文件
include __DIR__ . '/config.php';

// 处理OPTIONS请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// 检查是否是POST请求
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => 'Method not allowed']);
    exit;
}

// 获取参数并进行安全验证
$spaceId = trim($_POST['space_id'] ?? '');

// 验证space_id格式
if (!isValidSpaceId($spaceId)) {
    http_response_code(400);
    echo json_encode(['error' => 'Invalid space_id']);
    exit;
}

// 检查文件上传并验证文件类型
if (!isset($_FILES['photo']) || $_FILES['photo']['error'] !== UPLOAD_ERR_OK) {
    http_response_code(400);
    echo json_encode(['error' => 'No photo uploaded']);
    exit;
}

// 验证文件类型和大小
if (!isValidFileType($_FILES['photo']['type'], $_FILES['photo']['name'])) {
    http_response_code(400);
    echo json_encode(['error' => 'Invalid file type']);
    exit;
}

if ($_FILES['photo']['size'] > MAX_FILE_SIZE) {
    http_response_code(400);
    echo json_encode(['error' => 'File too large']);
    exit;
}

$dataDir = __DIR__ . '/data';
$imgDir = __DIR__ . '/../../../tpimg';
$dataFile = $dataDir . '/' . $spaceId . '.json';

// 检查空间是否存在
if (!file_exists($dataFile)) {
    http_response_code(404);
    echo json_encode(['error' => 'Space not found']);
    exit;
}

// 读取空间数据
$spaceData = json_decode(file_get_contents($dataFile), true);
if (!$spaceData) {
    http_response_code(500);
    echo json_encode(['error' => 'Invalid space data']);
    exit;
}

// 生成安全的唯一文件名
$fileExtension = 'jpg';
$fileName = $spaceId . '_' . time() . '_' . bin2hex(random_bytes(8)) . '.' . $fileExtension;
$filePath = $imgDir . '/' . $fileName;

// 确保文件名不存在（避免冲突）
while (file_exists($filePath)) {
    $fileName = $spaceId . '_' . time() . '_' . bin2hex(random_bytes(8)) . '.' . $fileExtension;
    $filePath = $imgDir . '/' . $fileName;
}

// 移动上传的文件
if (move_uploaded_file($_FILES['photo']['tmp_name'], $filePath)) {
    // 更新空间数据
    $spaceData['images'][] = $fileName;
    $spaceData['view_count']++;
    $spaceData['last_access'] = date('Y-m-d H:i:s');
    
    // 保存更新后的数据
    file_put_contents($dataFile, json_encode($spaceData, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT));
    
    echo json_encode(['success' => true, 'filename' => $fileName]);
} else {
    http_response_code(500);
    echo json_encode(['error' => 'Failed to save photo']);
}
?>
