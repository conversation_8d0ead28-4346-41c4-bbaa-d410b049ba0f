<?php
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');
header('Content-Type: application/json; charset=utf-8');

// 引入配置和验证文件
include __DIR__ . '/config.php';
include './verify_vip.php';

// 获取请求参数并进行安全验证
$token = trim($_GET['token'] ?? '');
$pw = trim($_GET['pw'] ?? '');
$action = trim($_GET['action'] ?? 'create'); // create 或 view

// 验证action参数
if (!in_array($action, ['create', 'view'])) {
    echo json_encode(['code' => 400, 'message' => '无效的操作类型'], JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
    exit;
}

// 验证Token格式
if (!isValidToken($token)) {
    echo json_encode(['code' => 400, 'message' => '无效的Token格式'], JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
    exit;
}

// 检查请求频率限制
if (!checkRateLimit($token, $action)) {
    echo json_encode(['code' => 429, 'message' => '请求过于频繁，请稍后再试'], JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
    exit;
}

// 数据存储目录
$dataDir = __DIR__ . '/data';
$imgDir = __DIR__ . '/../../../tpimg';

// 确保目录存在并设置正确权限
if (!is_dir($dataDir)) {
    if (!mkdir($dataDir, DIR_PERMISSIONS, true)) {
        echo json_encode(['code' => 500, 'message' => '无法创建数据目录'], JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
        exit;
    }
}
if (!is_dir($imgDir)) {
    if (!mkdir($imgDir, DIR_PERMISSIONS, true)) {
        echo json_encode(['code' => 500, 'message' => '无法创建图片目录'], JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
        exit;
    }
}

// 定期清理过期空间（10%的概率执行）
if (rand(1, 100) <= 10) {
    cleanExpiredSpaces($dataDir);
}

// 验证Token（仅在创建时需要）
if ($action === 'create') {
    if (empty($token)) {
        echo json_encode(['code' => 400, 'message' => '缺少token参数'], JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
        exit;
    }

    // 自定义限制
    $vipTimeLimit = false;
    $vipCodeLimit = true;

    // 定义回调函数
    $callback = function($vipcode, $viptime) use ($vipTimeLimit, $vipCodeLimit) {
        if ($vipCodeLimit) {
            $result = vipCodeLimit($vipcode);
            if ($result !== true) return $result;
        }

        if ($vipTimeLimit) {
            $result = vipTimeLimit($viptime);
            if ($result !== true) return $result;
        }

        return true;
    };

    // 调用验证函数
    $verificationResult = verifyVipStatus($token, $callback);

    if ($verificationResult !== true) {
        echo json_encode($verificationResult, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
        exit;
    }

    // 创建临时空间
    createTempSpace($token, $dataDir);
} elseif ($action === 'view') {
    // 查看图片
    if (empty($token) || empty($pw)) {
        echo json_encode(['code' => 400, 'message' => '缺少token或pw参数'], JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
        exit;
    }
    
    viewImages($token, $pw, $dataDir, $imgDir);
} else {
    echo json_encode(['code' => 400, 'message' => '无效的action参数'], JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
    exit;
}

// 创建临时空间函数
function createTempSpace($token, $dataDir) {
    // 生成唯一ID
    $spaceId = generateUniqueId();
    
    // 生成密码
    $password = generatePassword();
    
    // 创建数据记录
    $spaceData = [
        'id' => $spaceId,
        'token' => $token,
        'password' => $password,
        'created_at' => date('Y-m-d H:i:s'),
        'images' => [],
        'view_count' => 0
    ];
    
    // 保存到JSON文件
    $dataFile = $dataDir . '/' . $spaceId . '.json';
    file_put_contents($dataFile, json_encode($spaceData, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT));
    
    // 记录操作日志
    logOperation('CREATE_SPACE', $token, $spaceId, 'success');

    // 返回结果 - 强制使用HTTPS
    echo json_encode([
        'code' => 200,
        'message' => '临时空间创建成功',
        'tpurl' => getSecureUrl('/tmpview/' . $spaceId),
        'pw' => $password
    ], JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
}

// 查看图片函数
function viewImages($token, $password, $dataDir, $imgDir) {
    // 查找对应的空间数据
    $files = glob($dataDir . '/*.json');
    $spaceData = null;
    $spaceFile = null;
    
    foreach ($files as $file) {
        $data = json_decode(file_get_contents($file), true);
        if ($data && $data['token'] === $token && $data['password'] === $password) {
            $spaceData = $data;
            $spaceFile = $file;
            break;
        }
    }
    
    if (!$spaceData) {
        echo json_encode(['code' => 404, 'message' => 'Token或密码错误'], JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
        return;
    }
    
    // 构建图片URL数组 - 强制使用HTTPS
    $imgUrls = [];

    foreach ($spaceData['images'] as $imgFile) {
        if (file_exists($imgDir . '/' . $imgFile)) {
            $imgUrls[] = getSecureUrl('/tpimg/' . $imgFile);
        }
    }

    // 记录操作日志
    logOperation('VIEW_IMAGES', $token, $spaceData['id'], 'success');

    echo json_encode([
        'code' => 200,
        'message' => '获取成功',
        'imgurl' => $imgUrls,
        'count' => count($imgUrls)
    ], JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
}

// 生成唯一ID - 更安全的生成方式（不包含点号）
function generateUniqueId() {
    return uniqid(mt_rand(), false) . bin2hex(random_bytes(8));
}

// 生成密码 - 更安全的密码生成
function generatePassword() {
    $chars = 'abcdefghijklmnopqrstuvwxyz0123456789';
    $password = '';
    for ($i = 0; $i < 8; $i++) {
        $password .= $chars[random_int(0, strlen($chars) - 1)];
    }
    return $password;
}

// 获取基础URL - 强制使用HTTPS
function getBaseUrl() {
    return 'https://' . $_SERVER['HTTP_HOST'];
}
?>
