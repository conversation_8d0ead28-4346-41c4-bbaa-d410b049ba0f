-- 临时空间数据表（可选，如果想使用数据库而不是JSON文件）

CREATE TABLE IF NOT EXISTS `temp_spaces` (
    `id` varchar(50) NOT NULL PRIMARY KEY,
    `token` varchar(100) NOT NULL,
    `password` varchar(20) NOT NULL,
    `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
    `last_access` timestamp NULL,
    `view_count` int(11) DEFAULT 0,
    INDEX `idx_token` (`token`),
    INDEX `idx_token_password` (`token`, `password`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE IF NOT EXISTS `temp_space_images` (
    `id` int(11) NOT NULL AUTO_INCREMENT PRIMARY KEY,
    `space_id` varchar(50) NOT NULL,
    `filename` varchar(255) NOT NULL,
    `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
    FOREIG<PERSON> KEY (`space_id`) REFERENCES `temp_spaces`(`id`) ON DELETE CASCADE,
    INDEX `idx_space_id` (`space_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
