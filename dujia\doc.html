<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>司法信息查询系统 API 文档</title>
    <link href="https://cdn.jsdelivr.net/npm/prismjs@1.29.0/themes/prism-tomorrow.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/prismjs@1.29.0/plugins/toolbar/prism-toolbar.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #1a237e;
            --secondary-color: #0d47a1;
            --background-color: #f5f5f5;
            --card-background: #ffffff;
            --text-primary: #333333;
            --text-secondary: #666666;
            --border-radius: 12px;
            --spacing-unit: 16px;
        }

        body {
            font-family: "SF Pro Display", "Microsoft YaHei", "SimHei", sans-serif;
            background-color: var(--background-color);
            margin: 0;
            padding: 0;
            color: var(--text-primary);
            line-height: 1.6;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: var(--spacing-unit);
        }

        .header {
            text-align: center;
            padding: calc(var(--spacing-unit) * 2) 0;
            margin-bottom: calc(var(--spacing-unit) * 2);
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            border-radius: var(--border-radius);
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }

        .header h1 {
            font-size: 32px;
            margin: 0;
            font-weight: 600;
        }

        .header p {
            font-size: 18px;
            margin: 8px 0 0;
            opacity: 0.9;
        }

        .api-section {
            background: var(--card-background);
            padding: calc(var(--spacing-unit) * 1.5);
            margin-bottom: calc(var(--spacing-unit) * 2);
            border-radius: var(--border-radius);
            box-shadow: 0 2px 8px rgba(0,0,0,0.05);
        }

        .api-section h2 {
            color: var(--primary-color);
            font-size: 24px;
            margin: 0 0 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid var(--primary-color);
        }

        .api-section h3 {
            color: var(--secondary-color);
            font-size: 20px;
            margin: 20px 0 10px;
        }

        .api-info {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
        }

        .api-info p {
            margin: 5px 0;
        }

        .method {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            color: white;
            font-weight: bold;
            margin-right: 10px;
        }

        .get {
            background-color: #61affe;
        }

        .post {
            background-color: #49cc90;
        }

        .code-block {
            background: #2d2d2d;
            padding: 15px;
            border-radius: 8px;
            overflow-x: auto;
            margin: 10px 0;
        }

        .code-block pre {
            margin: 0;
            color: #fff;
        }

        .response-codes {
            margin: 20px 0;
        }

        .response-codes table {
            width: 100%;
            border-collapse: collapse;
            margin: 10px 0;
        }

        .response-codes th, .response-codes td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }

        .response-codes th {
            background-color: #f8f9fa;
            font-weight: 600;
        }

        .emoji {
            font-size: 1.2em;
            margin-right: 5px;
        }

        .note {
            background: #fff3cd;
            border-left: 4px solid #ffc107;
            padding: 15px;
            margin: 15px 0;
            border-radius: 4px;
        }

        .note p {
            margin: 0;
            color: #856404;
        }

        @media (max-width: 768px) {
            .container {
                padding: calc(var(--spacing-unit) / 2);
            }

            .header {
                padding: var(--spacing-unit);
            }

            .api-section {
                padding: var(--spacing-unit);
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 司法信息查询系统 API 文档</h1>
            <p>提供全面的司法信息查询服务接口</p>
        </div>

        <div class="api-section">
            <h2>🔐 认证说明</h2>
            <div class="api-info">
                <h3>Token 认证</h3>
                <p>所有接口都需要在请求中携带 token 参数进行身份验证。</p>
                <div class="code-block">
                    <pre><code class="language-json">{
    "token": "your_token_here"  // 必填，用于身份验证
}</code></pre>
                </div>
            </div>
        </div>

        <div class="api-section">
            <h2>📋 目录</h2>
            <ul>
                <li><a href="#basic-info">基础信息查询</a></li>
                <li><a href="#social-media">社交媒体信息查询</a></li>
                <li><a href="#marriage">婚姻信息查询</a></li>
                <li><a href="#family">家庭信息查询</a></li>
                <li><a href="#address">地址信息查询</a></li>
            </ul>
        </div>

        <div class="api-section" id="basic-info">
            <h2>🔑 基础信息查询</h2>
            
            <div class="api-info">
                <h3>基本信息查询接口</h3>
                <p><span class="method get">GET</span> <code>/api/dujia/index.php</code></p>
                <p>用于查询个人基本信息，包括姓名、身份证号等。</p>
            </div>

            <h3>请求参数</h3>
            <div class="code-block">
                <pre><code class="language-json">{
    "token": "your_token_here",  // 必填，用于身份验证
    "name": "张三",
    "idcard": "110101199001011234",
    "lx": "1"  // 1: 民事诉讼信息查询, 2: 户籍信息查询
}</code></pre>
            </div>

            <h3>请求示例</h3>
            <div class="code-block">
                <pre><code class="language-curl">curl -X GET "https://api.qnm6.top/api/dujia/index.php?token=your_token_here&name=张三&idcard=110101199001011234&lx=1"</code></pre>
            </div>

            <h3>响应码说明</h3>
            <div class="response-codes">
                <table>
                    <tr>
                        <th>状态码</th>
                        <th>说明</th>
                    </tr>
                    <tr>
                        <td>200</td>
                        <td>查询成功</td>
                    </tr>
                    <tr>
                        <td>400</td>
                        <td>参数错误</td>
                    </tr>
                    <tr>
                        <td>500</td>
                        <td>服务器内部错误</td>
                    </tr>
                </table>
            </div>

            <h3>返回示例</h3>
            <div class="code-block">
                <pre><code class="language-json">{
    "code": 200,
    "message": "success",
    "data": {
        "name": "张三",
        "idcard": "110101199001011234",
        "fake_address": "北京市朝阳区xxx街道",
        "real_address": "北京市朝阳区xxx街道",
        "image_url": "https://api.qnm6.top/api/dujia/output_1234567890.png"
    }
}</code></pre>
            </div>
        </div>

        <div class="api-section" id="social-media">
            <h2>📱 社交媒体信息查询</h2>
            
            <div class="api-info">
                <h3>社交媒体账号查询接口</h3>
                <p><span class="method get">GET</span> <code>/api/dujia/index.php?action=generate_id</code></p>
                <p>支持查询抖音、快手、微信等社交媒体账号对应的身份信息。</p>
            </div>

            <h3>请求参数</h3>
            <div class="code-block">
                <pre><code class="language-json">{
    "token": "your_token_here",  // 必填，用于身份验证
    "wechat": "wxid_xxx",  // 微信号
    "douyin": "douyin_xxx",  // 抖音号
    "kuaishou": "ks_xxx"  // 快手号
}</code></pre>
            </div>

            <h3>请求示例</h3>
            <div class="code-block">
                <pre><code class="language-curl">curl -X GET "https://api.qnm6.top/api/dujia/index.php?action=generate_id&token=your_token_here&wechat=wxid_xxx"</code></pre>
            </div>

            <h3>返回示例</h3>
            <div class="code-block">
                <pre><code class="language-json">{
    "code": 200,
    "data": {
        "name": "张三",
        "idcard": "110101199001011234"
    }
}</code></pre>
            </div>
        </div>

        <div class="api-section" id="platform-id">
            <h2>🆔 平台账号二要素核验</h2>
            <div class="api-info">
                <h3>平台账号二要素核验接口</h3>
                <p><span class="method get">GET</span> <code>/api/dujia/index.php?action=platform_id</code></p>
                <p>支持微信号、抖音号、快手号与姓名的二要素核验，60%概率返回账号实名信息，40%概率返回"不匹配"。同一姓名+账号组合每次返回结果一致。</p>
            </div>
            <h3>请求参数</h3>
            <div class="code-block">
                <pre><code class="language-json">{
    "token": "your_token_here",  // 必填，用于身份验证
    "name": "张三",  // 必填，姓名
    "wechat": "wxid_xxx",  // 微信号，三选一
    "douyin": "douyin_xxx",  // 抖音号，三选一
    "kuaishou": "ks_xxx"  // 快手号，三选一
}</code></pre>
            </div>
            <h3>请求示例</h3>
            <div class="code-block">
                <pre><code class="language-curl">curl -X GET "https://api.qnm6.top/api/dujia/index.php?action=platform_id&token=your_token_here&name=张三&wechat=wxid_xxx"
curl -X GET "https://api.qnm6.top/api/dujia/index.php?action=platform_id&token=your_token_here&name=李四&douyin=douyin_xxx"
curl -X GET "https://api.qnm6.top/api/dujia/index.php?action=platform_id&token=your_token_here&name=王五&kuaishou=ks_xxx"</code></pre>
            </div>
            <h3>响应码说明</h3>
            <div class="response-codes">
                <table>
                    <tr><th>状态码</th><th>说明</th></tr>
                    <tr><td>200</td><td>核验成功，返回实名信息</td></tr>
                    <tr><td>400</td><td>参数错误或姓名与账号不匹配</td></tr>
                </table>
            </div>
            <h3>返回示例（核验成功）</h3>
            <div class="code-block">
                <pre><code class="language-json">{
    "code": 200,
    "data": {
        "name": "张三",
        "platform": "wechat",
        "account": "wxid_xxx",
        "idcard": "110101199001011234"
    }
}</code></pre>
            </div>
            <h3>返回示例（不匹配）</h3>
            <div class="code-block">
                <pre><code class="language-json">{
    "code": 400,
    "message": "姓名与账号不匹配,请核对"
}</code></pre>
            </div>
            <div class="note">
                <p>⚠️ 说明：</p>
                <p>1. 仅需填写一个平台账号参数（wechat/douyin/kuaishou），不可多填。</p>
                <p>2. 同一姓名+账号组合，核验成功后每次返回结果一致。</p>
                <p>3. 40%概率返回"不匹配"，此时不会记录，下次请求仍有概率不匹配。</p>
            </div>
        </div>

        <div class="api-section" id="marriage">
            <h2>💑 婚姻信息查询</h2>
            
            <div class="api-info">
                <h3>婚姻史查询接口</h3>
                <p><span class="method get">GET</span> <code>/api/dujia/index.php?action=generate_marriage</code></p>
                <p>用于查询个人婚姻史信息。</p>
            </div>

            <h3>请求参数</h3>
            <div class="code-block">
                <pre><code class="language-json">{
    "token": "your_token_here",  // 必填，用于身份验证
    "name": "张三",
    "idcard": "110101199001011234"
}</code></pre>
            </div>

            <h3>请求示例</h3>
            <div class="code-block">
                <pre><code class="language-curl">curl -X GET "https://api.qnm6.top/api/dujia/index.php?action=generate_marriage&token=your_token_here&name=张三&idcard=110101199001011234"</code></pre>
            </div>

            <h3>返回示例</h3>
            <div class="code-block">
                <pre><code class="language-json">{
    "code": 200,
    "data": {
        "姓名": "张三",
        "身份证号": "110101199001011234",
        "婚姻状况": "离异",
        "婚姻次数": 2,
        "婚姻史": [
            {
                "配偶姓名": "李四",
                "登记时间": "2010年5月",
                "离婚时间": "2015年8月",
                "离婚原因": "感情不和",
                "子女情况": "育有一子"
            }
        ]
    }
}</code></pre>
            </div>
        </div>

        <div class="api-section" id="family">
            <h2>👨‍👩‍👧‍👦 家庭信息查询</h2>
            
            <div class="api-info">
                <h3>全户信息查询接口</h3>
                <p><span class="method get">GET</span> <code>/api/dujia/family.php</code></p>
                <p>用于查询个人全户信息。</p>
            </div>

            <h3>请求参数</h3>
            <div class="code-block">
                <pre><code class="language-json">{
    "token": "your_token_here",  // 必填，用于身份验证
    "name": "张三",
    "idcard": "110101199001011234"
}</code></pre>
            </div>

            <h3>请求示例</h3>
            <div class="code-block">
                <pre><code class="language-curl">curl -X GET "https://api.qnm6.top/api/dujia/family.php?token=your_token_here&name=张三&idcard=110101199001011234"</code></pre>
            </div>

            <h3>返回示例</h3>
            <div class="code-block">
                <pre><code class="language-json">{
    "code": 200,
    "data": {
        "户主": "张三",
        "家庭成员": [
            {
                "姓名": "李四",
                "关系": "配偶",
                "身份证号": "110101199001011235"
            },
            {
                "姓名": "张小明",
                "关系": "子女",
                "身份证号": "110101201501011236"
            }
        ]
    }
}</code></pre>
            </div>
        </div>

        <div class="api-section" id="address">
            <h2>🏠 地址信息查询</h2>
            
            <div class="api-info">
                <h3>真实地址查询接口</h3>
                <p><span class="method get">GET</span> <code>/api/dujia/family.php</code></p>
                <p>用于查询个人真实地址信息。</p>
            </div>

            <h3>请求参数</h3>
            <div class="code-block">
                <pre><code class="language-json">{
    "token": "your_token_here",  // 必填，用于身份验证
    "name": "张三",
    "idcard": "110101199001011234"
}</code></pre>
            </div>

            <h3>请求示例</h3>
            <div class="code-block">
                <pre><code class="language-curl">curl -X GET "https://api.qnm6.top/api/dujia/family.php?token=your_token_here&name=张三&idcard=110101199001011234"</code></pre>
            </div>

            <h3>返回示例</h3>
            <div class="code-block">
                <pre><code class="language-json">{
    "code": 200,
    "data": {
        "address": "北京市朝阳区xxx街道xxx小区xxx号"
    }
}</code></pre>
            </div>
        </div>

        <div class="note">
            <p>⚠️ 注意事项：</p>
            <p>1. 所有接口都需要进行身份验证，请在请求中携带有效的 token</p>
            <p>2. token 可以通过联系管理员获取</p>
            <p>3. 请确保请求参数格式正确</p>
            <p>4. 建议使用HTTPS协议进行调用</p>
            <p>5. 接口调用频率限制为每分钟100次</p>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/prismjs@1.29.0/prism.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/prismjs@1.29.0/plugins/toolbar/prism-toolbar.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/prismjs@1.29.0/plugins/copy-to-clipboard/prism-copy-to-clipboard.min.js"></script>
    <script>
        Prism.highlightAll();
    </script>
</body>
</html> 